import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  Legend
} from 'recharts';
import { toast } from 'react-toastify';
import { Users, Eye, TrendingUp, Clock } from 'lucide-react';

const ClientPerformance = ({ timeRange }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [clientData, setClientData] = useState(null);

  useEffect(() => {
    const fetchClientData = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        console.log('Fetching client analytics with params:', {
          start: start.toISOString(),
          end: end.toISOString(),
          apiUrl: `${apiUrl}/api/analytics/admin/clients`
        });

        const response = await fetch(`${apiUrl}/api/analytics/admin/clients?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => null);
          console.error('API Error Response:', {
            status: response.status,
            statusText: response.statusText,
            errorData
          });
          throw new Error(`Failed to fetch client performance data: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Received client analytics data:', data);

        if (!data || Object.keys(data).length === 0) {
          console.warn('Received empty data from API');
        }

        // Calculate summary metrics from the client data array
        const totalClients = data.length;
        const totalSessions = data.reduce((sum, client) => sum + (client.sessions || 0), 0);
        const avgSessionDuration = data.length > 0 ?
          data.reduce((sum, client) => sum + (client.avgDuration || 0), 0) / data.length : 0;

        setClientData({
          clients: data,
          totalClients,
          totalSessions,
          avgSessionDuration,
          topClients: data.slice(0, 10).map(client => ({
            name: client.clientName || client.email || 'Unknown Client',
            sessions: client.sessions,
            avgDuration: client.avgDuration || 0
          })),
          // Growth metrics (set to 0 since we don't have previous period data)
          clientsGrowth: 0,
          sessionsGrowth: 0,
          durationGrowth: 0
        });
      } catch (err) {
        console.error('Error fetching client performance:', err);
        setError(err.message);
        toast.error(`Error loading analytics: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchClientData();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading client performance</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Clients</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {clientData?.totalClients?.toLocaleString() || '0'}
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
              <Users className="h-6 w-6 text-[#2D8C88]" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${clientData?.clientsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {clientData?.clientsGrowth >= 0 ? '+' : ''}{clientData?.clientsGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Sessions</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {clientData?.totalSessions?.toLocaleString() || '0'}
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
              <Eye className="h-6 w-6 text-blue-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${clientData?.sessionsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {clientData?.sessionsGrowth >= 0 ? '+' : ''}{clientData?.sessionsGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Session Duration</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {Math.round(clientData?.avgSessionDuration || 0)}s
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
              <Clock className="h-6 w-6 text-green-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${clientData?.durationGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {clientData?.durationGrowth >= 0 ? '+' : ''}{clientData?.durationGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>


      </div>

      {/* Client Performance Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-xl shadow-sm overflow-hidden"
      >
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Client Performance</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Duration</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {clientData?.topClients?.map((client, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white">
                          {client.name.charAt(0)}
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{client.name}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {client.sessions?.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {Math.round(client.avgDuration)}s
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>

      {/* Client Session Distribution */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-white rounded-xl shadow-sm p-6"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-4">Client Session Distribution</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={clientData?.topClients}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip />
              <Bar dataKey="sessions" fill="#2D8C88" name="Sessions" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </motion.div>
    </div>
  );
};

export default ClientPerformance;
