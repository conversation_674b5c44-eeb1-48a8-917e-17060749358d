import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  LineChart,
  Line
} from 'recharts';
import { toast } from 'react-toastify';
import { Eye, Clock, TrendingUp, Smartphone, Monitor } from 'lucide-react';

const TryOnAnalytics = ({ timeRange }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tryOnData, setTryOnData] = useState(null);

  useEffect(() => {
    const fetchTryOnData = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        
        // Ensure we're using current dates
        const now = new Date();
        end.setTime(now.getTime());
        
        switch (timeRange) {
          case '7d':
            start.setTime(now.getTime() - (7 * 24 * 60 * 60 * 1000));
            break;
          case '30d':
            start.setTime(now.getTime() - (30 * 24 * 60 * 60 * 1000));
            break;
          case '90d':
            start.setTime(now.getTime() - (90 * 24 * 60 * 60 * 1000));
            break;
          case '1y':
            start.setTime(now.getTime() - (365 * 24 * 60 * 60 * 1000));
            break;
          default:
            start.setTime(now.getTime() - (7 * 24 * 60 * 60 * 1000));
        }

        console.log('Fetching try-on analytics with params:', {
          start: start.toISOString(),
          end: end.toISOString(),
          apiUrl: `${apiUrl}/api/analytics/admin/tryon`,
          timeRange
        });

        const response = await fetch(`${apiUrl}/api/analytics/admin/tryon?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => null);
          console.error('API Error Response:', {
            status: response.status,
            statusText: response.statusText,
            errorData
          });
          throw new Error(`Failed to fetch try-on analytics data: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Received try-on analytics data:', data);

        if (!data || Object.keys(data).length === 0) {
          console.warn('Received empty data from API');
        }

        // Transform the API data into the format our UI expects
        const transformedData = {
          totalSessions: data.dailyStats?.reduce((sum, day) => sum + (day.sessions || 0), 0) || 0,
          sessionsGrowth: data.dailyStats?.length > 1
            ? ((data.dailyStats[data.dailyStats.length - 1].sessions - data.dailyStats[0].sessions) / (data.dailyStats[0].sessions || 1)) * 100
            : 0,
          avgSessionDuration: data.dailyStats?.reduce((sum, day) => sum + (day.avgDuration || 0), 0) / (data.dailyStats?.length || 1) || 0,
          durationGrowth: data.dailyStats?.length > 1
            ? ((data.dailyStats[data.dailyStats.length - 1].avgDuration - data.dailyStats[0].avgDuration) / (data.dailyStats[0].avgDuration || 1)) * 100
            : 0,
          successRate: data.dailyStats?.length > 0
            ? data.dailyStats.reduce((sum, day) => sum + ((day.conversions || 0) / (day.sessions || 1)), 0) / data.dailyStats.length
            : 0,
          successRateGrowth: 0, // Will calculate if we have enough data
          mobileUsage: 0.6, // Default value since we don't have device breakdown in this endpoint
          mobileUsageGrowth: 0,
          sessionTrends: data.dailyStats?.map(day => ({
            date: day.date,
            sessions: day.sessions || 0,
            successfulSessions: day.conversions || 0
          })) || [],
          deviceStats: [
            { name: 'Mobile', value: 60 }, // Default values since device data isn't in this endpoint
            { name: 'Desktop', value: 40 }
          ],
          featureSuccessRates: null, // Remove since we don't have this data
          productCategorySuccess: data.categoryStats?.map(cat => ({
            category: cat.category,
            successRate: cat.conversionRate || 0,
            sessions: cat.count || 0
          })) || [],
          conversionRate: data.dailyStats?.length > 0
            ? data.dailyStats.reduce((sum, day) => sum + ((day.conversions || 0) / (day.sessions || 1)), 0) / data.dailyStats.length
            : 0,
          avgProductViewTime: data.dailyStats?.reduce((sum, day) => sum + (day.avgProducts || 0), 0) / (data.dailyStats?.length || 1) || 0
        };

        // Only use real data from backend
        setTryOnData(transformedData);
      } catch (err) {
        console.error('Error fetching try-on analytics:', err);
        setError(err.message);
        toast.error(`Error loading analytics: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchTryOnData();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading try-on analytics</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const deviceColors = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Sessions</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {tryOnData?.totalSessions?.toLocaleString() || '0'}
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
              <Eye className="h-6 w-6 text-[#2D8C88]" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${tryOnData?.sessionsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {tryOnData?.sessionsGrowth >= 0 ? '+' : ''}{tryOnData?.sessionsGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Session Duration</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {Math.round(tryOnData?.avgSessionDuration || 0)}s
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
              <Clock className="h-6 w-6 text-blue-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${tryOnData?.durationGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {tryOnData?.durationGrowth >= 0 ? '+' : ''}{tryOnData?.durationGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        {/* Only show Mobile Usage if we have device data */}
        {tryOnData?.mobileUsage && tryOnData.mobileUsage > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-xl shadow-sm p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Mobile Usage</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">
                  {(tryOnData.mobileUsage * 100).toFixed(1)}%
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                <Smartphone className="h-6 w-6 text-purple-500" />
              </div>
            </div>
            <div className="mt-4">
              <span className={`text-sm font-medium ${tryOnData?.mobileUsageGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {tryOnData?.mobileUsageGrowth >= 0 ? '+' : ''}{tryOnData?.mobileUsageGrowth?.toFixed(1) || '0'}%
              </span>
              <span className="text-sm text-gray-600 ml-2">from previous period</span>
            </div>
          </motion.div>
        )}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Session Trends */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Trends</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={tryOnData?.sessionTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="sessions"
                  stroke="#2D8C88"
                  strokeWidth={2}
                  dot={{ fill: '#2D8C88' }}
                  name="Sessions"
                />

              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Device Distribution */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Device Distribution</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={tryOnData?.deviceStats}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {tryOnData?.deviceStats?.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={deviceColors[index % deviceColors.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-4">
            {tryOnData?.deviceStats?.map((device, index) => (
              <div key={index} className="text-center">
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: deviceColors[index % deviceColors.length] }}></div>
                  <span className="text-sm font-medium text-gray-900">{device.name}</span>
                </div>
                <p className="text-lg font-semibold text-gray-900 mt-1">{device.value}%</p>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Feature Success Rates - Only show if we have data */}
      {tryOnData?.featureSuccessRates && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Feature Success Rates</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={tryOnData.featureSuccessRates}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="feature" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="successRate" fill="#2D8C88" name="Success Rate" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      )}

      {/* Product Category Sessions Chart - Only show if we have data */}
      {tryOnData?.productCategorySuccess && tryOnData.productCategorySuccess.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Product Category Sessions</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={tryOnData.productCategorySuccess}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="sessions" fill="#2D8C88" name="Total Sessions" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      )}


    </div>
  );
};

export default TryOnAnalytics;
