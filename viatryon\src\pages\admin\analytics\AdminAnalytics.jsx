import React, { useState, useEffect, useRef } from 'react';
import { Routes, Route, useLocation, useNavigate } from 'react-router-dom';
import AdminSidebar from '../../../components/admin/AdminSidebar';
import AdminNavbar from '../../../components/admin/AdminNavbar';
import Overview from './Overview';
import TryOnAnalytics from './TryOnAnalytics';
import ClientPerformance from './ClientPerformance';
import ProductAnalytics from './ProductAnalytics';

import BehaviorAnalytics from './BehaviorAnalytics';
import GeographicAnalytics from './GeographicAnalytics';

const AdminAnalytics = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');
  const location = useLocation();
  const navigate = useNavigate();
  const sidebarRef = useRef(null);

  const mainMargin = collapsed ? 'md:ml-20' : 'md:ml-72';

  const analyticsTabs = [
    { name: 'Overview', path: '/admin/analytics/overview' },
    { name: 'Try-On Analytics', path: '/admin/analytics/tryon' },
    { name: 'Client Performance', path: '/admin/analytics/clients' },
    { name: 'Product Analytics', path: '/admin/analytics/products' },
    { name: 'User Behavior', path: '/admin/analytics/behavior' },
    { name: 'Geographic Analytics', path: '/admin/analytics/geographic' },
  ];

  // Auto-navigate to overview if on base analytics path
  useEffect(() => {
    if (location.pathname === '/admin/analytics' || location.pathname === '/admin/analytics/') {
      navigate('/admin/analytics/overview', { replace: true });
    }
  }, [location.pathname, navigate]);

  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar 
        ref={sidebarRef}
        collapsed={collapsed} 
        setCollapsed={setCollapsed} 
      />
      
      <div className={`transition-all duration-300 ${mainMargin}`}>
        <AdminNavbar />
        
        <main className="p-4 md:p-6 space-y-6">
          {/* Header */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Admin Analytics</h1>
                <p className="text-gray-600 mt-1">Comprehensive analytics across all clients and try-on sessions</p>
              </div>
              
              {/* Time Range Selector */}
              <div className="mt-4 md:mt-0">
                <select 
                  value={timeRange}
                  onChange={(e) => handleTimeRangeChange(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="7d">Last 7 days</option>
                  <option value="30d">Last 30 days</option>
                  <option value="90d">Last 90 days</option>
                  <option value="1y">Last year</option>
                </select>
              </div>
            </div>
          </div>

          {/* Analytics Tabs */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6 overflow-x-auto" aria-label="Analytics tabs">
                {analyticsTabs.map((tab) => {
                  const isActive = location.pathname === tab.path;
                  return (
                    <button
                      key={tab.name}
                      onClick={() => navigate(tab.path)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                        isActive
                          ? 'border-teal-500 text-teal-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {tab.name}
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Analytics Content */}
            <div className="p-6">
              <Routes>
                <Route path="/" element={<Overview timeRange={timeRange} />} />
                <Route path="/overview" element={<Overview timeRange={timeRange} />} />
                <Route path="/tryon" element={<TryOnAnalytics timeRange={timeRange} />} />
                <Route path="/clients" element={<ClientPerformance timeRange={timeRange} />} />
                <Route path="/products" element={<ProductAnalytics timeRange={timeRange} />} />
                <Route path="/behavior" element={<BehaviorAnalytics timeRange={timeRange} />} />

                <Route path="/geographic" element={<GeographicAnalytics timeRange={timeRange} />} />
              </Routes>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminAnalytics;
